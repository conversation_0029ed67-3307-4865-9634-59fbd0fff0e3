// Generated by view binder compiler. Do not edit!
package com.example.myapplication.databinding;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.cardview.widget.CardView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewbinding.ViewBinding;
import androidx.viewbinding.ViewBindings;
import com.example.myapplication.R;
import java.lang.NullPointerException;
import java.lang.Override;
import java.lang.String;

public final class ActivityMainBinding implements ViewBinding {
  @NonNull
  private final SwipeRefreshLayout rootView;

  @NonNull
  public final CardView cardView;

  @NonNull
  public final SwipeRefreshLayout swipeRefresh;

  @NonNull
  public final TextView tvAmount;

  @NonNull
  public final TextView tvAmountLabel;

  @NonNull
  public final TextView tvNum;

  @NonNull
  public final TextView tvNumLabel;

  @NonNull
  public final TextView tvStatus;

  @NonNull
  public final TextView tvTips;

  private ActivityMainBinding(@NonNull SwipeRefreshLayout rootView, @NonNull CardView cardView,
      @NonNull SwipeRefreshLayout swipeRefresh, @NonNull TextView tvAmount,
      @NonNull TextView tvAmountLabel, @NonNull TextView tvNum, @NonNull TextView tvNumLabel,
      @NonNull TextView tvStatus, @NonNull TextView tvTips) {
    this.rootView = rootView;
    this.cardView = cardView;
    this.swipeRefresh = swipeRefresh;
    this.tvAmount = tvAmount;
    this.tvAmountLabel = tvAmountLabel;
    this.tvNum = tvNum;
    this.tvNumLabel = tvNumLabel;
    this.tvStatus = tvStatus;
    this.tvTips = tvTips;
  }

  @Override
  @NonNull
  public SwipeRefreshLayout getRoot() {
    return rootView;
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater) {
    return inflate(inflater, null, false);
  }

  @NonNull
  public static ActivityMainBinding inflate(@NonNull LayoutInflater inflater,
      @Nullable ViewGroup parent, boolean attachToParent) {
    View root = inflater.inflate(R.layout.activity_main, parent, false);
    if (attachToParent) {
      parent.addView(root);
    }
    return bind(root);
  }

  @NonNull
  public static ActivityMainBinding bind(@NonNull View rootView) {
    // The body of this method is generated in a way you would not otherwise write.
    // This is done to optimize the compiled bytecode for size and performance.
    int id;
    missingId: {
      id = R.id.cardView;
      CardView cardView = ViewBindings.findChildViewById(rootView, id);
      if (cardView == null) {
        break missingId;
      }

      SwipeRefreshLayout swipeRefresh = (SwipeRefreshLayout) rootView;

      id = R.id.tvAmount;
      TextView tvAmount = ViewBindings.findChildViewById(rootView, id);
      if (tvAmount == null) {
        break missingId;
      }

      id = R.id.tvAmountLabel;
      TextView tvAmountLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvAmountLabel == null) {
        break missingId;
      }

      id = R.id.tvNum;
      TextView tvNum = ViewBindings.findChildViewById(rootView, id);
      if (tvNum == null) {
        break missingId;
      }

      id = R.id.tvNumLabel;
      TextView tvNumLabel = ViewBindings.findChildViewById(rootView, id);
      if (tvNumLabel == null) {
        break missingId;
      }

      id = R.id.tvStatus;
      TextView tvStatus = ViewBindings.findChildViewById(rootView, id);
      if (tvStatus == null) {
        break missingId;
      }

      id = R.id.tvTips;
      TextView tvTips = ViewBindings.findChildViewById(rootView, id);
      if (tvTips == null) {
        break missingId;
      }

      return new ActivityMainBinding((SwipeRefreshLayout) rootView, cardView, swipeRefresh,
          tvAmount, tvAmountLabel, tvNum, tvNumLabel, tvStatus, tvTips);
    }
    String missingId = rootView.getResources().getResourceName(id);
    throw new NullPointerException("Missing required view with ID: ".concat(missingId));
  }
}
