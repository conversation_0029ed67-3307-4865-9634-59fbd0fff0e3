<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res"><file name="ic_dashboard_black_24dp" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\drawable\ic_dashboard_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_home_black_24dp" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\drawable\ic_home_black_24dp.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_notifications_black_24dp" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\drawable\ic_notifications_black_24dp.xml" qualifiers="" type="drawable"/><file name="activity_login" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\layout\activity_login.xml" qualifiers="" type="layout"/><file name="activity_main" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="fragment_dashboard" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\layout\fragment_dashboard.xml" qualifiers="" type="layout"/><file name="fragment_home" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\layout\fragment_home.xml" qualifiers="" type="layout"/><file name="fragment_notifications" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\layout\fragment_notifications.xml" qualifiers="" type="layout"/><file name="bottom_nav_menu" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\menu\bottom_nav_menu.xml" qualifiers="" type="menu"/><file name="main_menu" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\menu\main_menu.xml" qualifiers="" type="menu"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-hdpi\ic_launcher.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-hdpi\ic_launcher_round.webp" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-mdpi\ic_launcher.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-mdpi\ic_launcher_round.webp" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-xhdpi\ic_launcher.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-xhdpi\ic_launcher_round.webp" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-xxhdpi\ic_launcher.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.webp" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-xxxhdpi\ic_launcher.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.webp" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="mobile_navigation" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\navigation\mobile_navigation.xml" qualifiers="" type="navigation"/><file path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\values\colors.xml" qualifiers=""><color name="purple_200">#FFBB86FC</color><color name="purple_500">#FF6200EE</color><color name="purple_700">#FF3700B3</color><color name="teal_200">#FF03DAC5</color><color name="teal_700">#FF018786</color><color name="black">#FF000000</color><color name="white">#FFFFFFFF</color></file><file path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\values\dimens.xml" qualifiers=""><dimen name="activity_horizontal_margin">16dp</dimen><dimen name="activity_vertical_margin">16dp</dimen></file><file path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">闲鱼收益监控</string><string name="title_home">首页</string><string name="title_dashboard">数据</string><string name="title_notifications">通知</string><string name="loading">加载中...</string><string name="login">登录</string><string name="logout">退出登录</string><string name="refresh">刷新</string><string name="order_count">订单数量</string><string name="amount">金额</string><string name="last_update">最近更新: </string><string name="auto_refresh_tip">提示：数据每10秒自动更新一次，也可以下拉刷新。</string></file><file path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_500</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/white</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_700</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\values-night\themes.xml" qualifiers="night-v8"><style name="Theme.MyApplication" parent="Theme.MaterialComponents.DayNight.DarkActionBar">
        
        <item name="colorPrimary">@color/purple_200</item>
        <item name="colorPrimaryVariant">@color/purple_700</item>
        <item name="colorOnPrimary">@color/black</item>
        
        <item name="colorSecondary">@color/teal_200</item>
        <item name="colorSecondaryVariant">@color/teal_200</item>
        <item name="colorOnSecondary">@color/black</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
        
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/><file name="network_security_config" path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\res\xml\network_security_config.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>