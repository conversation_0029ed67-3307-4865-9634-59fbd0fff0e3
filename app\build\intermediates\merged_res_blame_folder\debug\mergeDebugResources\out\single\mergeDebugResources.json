[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-xxxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\layout_fragment_notifications.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\fragment_notifications.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\menu_bottom_nav_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\menu\\bottom_nav_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\menu_main_menu.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\menu\\main_menu.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\layout_activity_main.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-hdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-hdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\navigation_mobile_navigation.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\navigation\\mobile_navigation.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\xml_network_security_config.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\xml\\network_security_config.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\layout_fragment_dashboard.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\fragment_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-mdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-mdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\layout_activity_login.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-xxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-xxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\drawable_ic_home_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\drawable\\ic_home_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\layout_fragment_home.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\fragment_home.xml"}, {"merged": "com.example.myapplication.app-debug-45:/layout_activity_login.xml.flat", "source": "com.example.myapplication.app-main-47:/layout/activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-xxhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\drawable_ic_launcher_foreground.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\drawable\\ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-xhdpi\\ic_launcher_round.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\xml_backup_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\xml\\backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-hdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-hdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-anydpi-v26\\ic_launcher.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\xml_data_extraction_rules.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\xml\\data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-xxxhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-mdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-mdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\drawable_ic_launcher_background.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\drawable\\ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\drawable_ic_dashboard_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\drawable\\ic_dashboard_black_24dp.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-anydpi-v26\\ic_launcher_round.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\mipmap-xhdpi_ic_launcher.webp.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\mipmap-xhdpi\\ic_launcher.webp"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-debug-45:\\drawable_ic_notifications_black_24dp.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\drawable\\ic_notifications_black_24dp.xml"}]