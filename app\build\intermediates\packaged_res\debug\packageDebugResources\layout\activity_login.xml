<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.login.LoginActivity">

    <!-- 隐藏WebView，仅用于处理请求和登录验证 -->
    <WebView
        android:id="@+id/webView"
        android:layout_width="1dp"
        android:layout_height="1dp"
        android:visibility="invisible"/>
    
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:gravity="center"
        android:padding="24dp">
        
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="闲鱼收益监控"
            android:textSize="24sp"
            android:textStyle="bold"
            android:gravity="center"
            android:layout_marginBottom="48dp"/>
            
        <ImageView
            android:id="@+id/qrCodeImageView"
            android:layout_width="240dp"
            android:layout_height="240dp"
            android:layout_gravity="center"
            android:contentDescription="微信登录二维码"/>
            
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="请使用微信扫描二维码登录"
            android:textSize="16sp"
            android:gravity="center"
            android:layout_marginTop="24dp"/>
            
        <TextView
            android:id="@+id/tvStatus"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="正在加载登录二维码..."
            android:textSize="14sp"
            android:gravity="center"
            android:layout_marginTop="16dp"/>
    
    </LinearLayout>
    
    <ProgressBar
        android:id="@+id/progressBar"
        style="?android:attr/progressBarStyleLarge"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"/>

</RelativeLayout> 