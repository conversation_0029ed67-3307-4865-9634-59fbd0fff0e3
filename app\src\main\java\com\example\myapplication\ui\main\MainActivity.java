package com.example.myapplication.ui.main;

import android.content.Intent;
import android.os.Bundle;
import android.view.Menu;
import android.view.MenuItem;
import android.view.View;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.example.myapplication.R;
import com.example.myapplication.data.SessionManager;
import com.example.myapplication.ui.login.LoginActivity;

public class MainActivity extends AppCompatActivity {

    private MainViewModel viewModel;
    private SessionManager sessionManager;
    private TextView tvNum;
    private TextView tvAmount;
    private TextView tvStatus;
    private SwipeRefreshLayout swipeRefreshLayout;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // Initialize views
        tvNum = findViewById(R.id.tvNum);
        tvAmount = findViewById(R.id.tvAmount);
        tvStatus = findViewById(R.id.tvStatus);
        swipeRefreshLayout = findViewById(R.id.swipeRefresh);

        // Initialize session manager
        sessionManager = new SessionManager(this);

        // Check if user is logged in
        if (!sessionManager.isLoggedIn()) {
            startLoginActivity();
            return;
        }

        // Initialize ViewModel
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);

        // Set up SwipeRefreshLayout
        swipeRefreshLayout.setOnRefreshListener(() -> {
            viewModel.fetchEarningsData();
        });

        // Observe loading state
        viewModel.isLoading().observe(this, isLoading -> {
            if (!isLoading) {
                swipeRefreshLayout.setRefreshing(false);
            }
        });

        // Observe data
        viewModel.getEarningsData().observe(this, earningsItem -> {
            if (earningsItem != null) {
                tvNum.setText(earningsItem.getNum());
                tvAmount.setText(earningsItem.getAmount());
                tvStatus.setText("最近更新: " + java.time.LocalTime.now().toString());
                tvStatus.setVisibility(View.VISIBLE);
            }
        });

        // Observe errors
        viewModel.getError().observe(this, error -> {
            if (error != null && !error.isEmpty()) {
                Toast.makeText(this, error, Toast.LENGTH_LONG).show();
                tvStatus.setText("更新失败: " + error);
                tvStatus.setVisibility(View.VISIBLE);
            }
        });

        // Start auto refresh
        viewModel.startAutoRefresh();
        
        // Initial data fetch
        viewModel.fetchEarningsData();
    }

    @Override
    public boolean onCreateOptionsMenu(Menu menu) {
        getMenuInflater().inflate(R.menu.main_menu, menu);
        return true;
    }

    @Override
    public boolean onOptionsItemSelected(@NonNull MenuItem item) {
        if (item.getItemId() == R.id.action_logout) {
            logout();
            return true;
        } else if (item.getItemId() == R.id.action_refresh) {
            viewModel.fetchEarningsData();
            return true;
        }
        return super.onOptionsItemSelected(item);
    }

    private void logout() {
        viewModel.stopAutoRefresh();
        sessionManager.clearSession();
        startLoginActivity();
    }

    private void startLoginActivity() {
        Intent intent = new Intent(this, LoginActivity.class);
        startActivity(intent);
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (viewModel != null) {
            viewModel.stopAutoRefresh();
        }
    }
} 