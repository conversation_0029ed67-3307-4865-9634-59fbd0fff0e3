1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- 添加网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:9:5-37:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:14:9-41
30        android:networkSecurityConfig="@xml/network_security_config"
30-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:18:9-69
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:15:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:16:9-35
33        android:testOnly="true"
34        android:theme="@style/Theme.MyApplication"
34-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:17:9-51
35        android:usesCleartextTraffic="true" >
35-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:19:9-44
36
37        <!-- 修改启动Activity为LoginActivity -->
38        <activity
38-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:22:9-31:20
39            android:name="com.example.myapplication.ui.login.LoginActivity"
39-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:23:13-51
40            android:exported="true"
40-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:24:13-36
41            android:label="@string/app_name" >
41-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:25:13-45
42            <intent-filter>
42-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:26:13-30:29
43                <action android:name="android.intent.action.MAIN" />
43-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:27:17-69
43-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:27:25-66
44
45                <category android:name="android.intent.category.LAUNCHER" />
45-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:29:17-77
45-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:29:27-74
46            </intent-filter>
47        </activity>
48
49        <!-- 添加MainActivity -->
50        <activity
50-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:34:9-36:40
51            android:name="com.example.myapplication.ui.main.MainActivity"
51-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:35:13-49
52            android:exported="false" />
52-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:36:13-37
53
54        <provider
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.example.myapplication.androidx-startup"
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1adb71351fcb0118fc71b359941a513a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1adb71351fcb0118fc71b359941a513a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1adb71351fcb0118fc71b359941a513a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <uses-library
69-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
70            android:name="androidx.window.extensions"
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
71            android:required="false" />
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
72        <uses-library
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
73            android:name="androidx.window.sidecar"
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
74            android:required="false" />
74-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
75
76        <receiver
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
77            android:name="androidx.profileinstaller.ProfileInstallReceiver"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
78            android:directBootAware="false"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
79            android:enabled="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
80            android:exported="true"
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
81            android:permission="android.permission.DUMP" >
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
82            <intent-filter>
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
83                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
84            </intent-filter>
85            <intent-filter>
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
86                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
87            </intent-filter>
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
89                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
92                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
93            </intent-filter>
94        </receiver>
95    </application>
96
97</manifest>
