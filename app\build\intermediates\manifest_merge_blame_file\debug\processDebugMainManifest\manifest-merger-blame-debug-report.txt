1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.myapplication"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="36" />
10
11    <!-- 添加网络权限 -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:7:22-76
14
15    <permission
15-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.example.myapplication.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:9:5-37:19
22        android:allowBackup="true"
22-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:10:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ab796b55928b01c99ef0f63574e84cd\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
24        android:dataExtractionRules="@xml/data_extraction_rules"
24-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:11:9-65
25        android:debuggable="true"
26        android:extractNativeLibs="false"
27        android:fullBackupContent="@xml/backup_rules"
27-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:12:9-54
28        android:icon="@mipmap/ic_launcher"
28-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:13:9-43
29        android:label="@string/app_name"
29-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:14:9-41
30        android:networkSecurityConfig="@xml/network_security_config"
30-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:18:9-69
31        android:roundIcon="@mipmap/ic_launcher_round"
31-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:15:9-54
32        android:supportsRtl="true"
32-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:16:9-35
33        android:theme="@style/Theme.MyApplication"
33-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:17:9-51
34        android:usesCleartextTraffic="true" >
34-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:19:9-44
35
36        <!-- 修改启动Activity为LoginActivity -->
37        <activity
37-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:22:9-31:20
38            android:name="com.example.myapplication.ui.login.LoginActivity"
38-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:23:13-51
39            android:exported="true"
39-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:24:13-36
40            android:label="@string/app_name" >
40-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:25:13-45
41            <intent-filter>
41-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:26:13-30:29
42                <action android:name="android.intent.action.MAIN" />
42-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:27:17-69
42-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:27:25-66
43
44                <category android:name="android.intent.category.LAUNCHER" />
44-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:29:17-77
44-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:29:27-74
45            </intent-filter>
46        </activity>
47
48        <!-- 添加MainActivity -->
49        <activity
49-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:34:9-36:40
50            android:name="com.example.myapplication.ui.main.MainActivity"
50-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:35:13-49
51            android:exported="false" />
51-->C:\Users\<USER>\Desktop\XianYuEarningsMonitor\app\src\main\AndroidManifest.xml:36:13-37
52
53        <provider
53-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
54            android:name="androidx.startup.InitializationProvider"
54-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
55            android:authorities="com.example.myapplication.androidx-startup"
55-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
56            android:exported="false" >
56-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
57            <meta-data
57-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
58                android:name="androidx.emoji2.text.EmojiCompatInitializer"
58-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
59                android:value="androidx.startup" />
59-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\56535ee6a5ff99d6b2fcab0faaa844e3\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
60            <meta-data
60-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1adb71351fcb0118fc71b359941a513a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
61                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
61-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1adb71351fcb0118fc71b359941a513a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
62                android:value="androidx.startup" />
62-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\1adb71351fcb0118fc71b359941a513a\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
63            <meta-data
63-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
64                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
64-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
65                android:value="androidx.startup" />
65-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
66        </provider>
67
68        <uses-library
68-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:25:9-27:40
69            android:name="androidx.window.extensions"
69-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:26:13-54
70            android:required="false" />
70-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:27:13-37
71        <uses-library
71-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:28:9-30:40
72            android:name="androidx.window.sidecar"
72-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:29:13-51
73            android:required="false" />
73-->[androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4d15c17a5f7e744e50596140fef1020d\transformed\window-1.0.0\AndroidManifest.xml:30:13-37
74
75        <receiver
75-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
76            android:name="androidx.profileinstaller.ProfileInstallReceiver"
76-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
77            android:directBootAware="false"
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
78            android:enabled="true"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
79            android:exported="true"
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
80            android:permission="android.permission.DUMP" >
80-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
82                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
85                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
86            </intent-filter>
87            <intent-filter>
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
88                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
89            </intent-filter>
90            <intent-filter>
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
91                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\39e37b6aa2aa9e0bc612327a8c34efaa\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
92            </intent-filter>
93        </receiver>
94    </application>
95
96</manifest>
