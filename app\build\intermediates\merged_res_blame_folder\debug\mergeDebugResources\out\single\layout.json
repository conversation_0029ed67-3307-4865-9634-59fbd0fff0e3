[{"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-mergeDebugResources-44:\\layout\\fragment_home.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\fragment_home.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-mergeDebugResources-44:\\layout\\fragment_notifications.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\fragment_notifications.xml"}, {"merged": "com.example.myapplication.app-mergeDebugResources-44:/layout/activity_login.xml", "source": "com.example.myapplication.app-main-47:/layout/activity_login.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-mergeDebugResources-44:\\layout\\activity_main.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\activity_main.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-mergeDebugResources-44:\\layout\\fragment_dashboard.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\fragment_dashboard.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-mergeDebugResources-44:\\layout\\activity_login.xml", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.13\\com.example.myapplication.app-main-47:\\layout\\activity_login.xml"}]