package com.example.myapplication.data.repository;

import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.myapplication.data.model.EarningsResponse;
import com.example.myapplication.data.network.ApiClient;
import com.example.myapplication.data.network.ApiService;

import retrofit2.Call;
import retrofit2.Callback;
import retrofit2.Response;

public class EarningsRepository {
    private static final String DEFAULT_VERSION = "3.54.31";
    private static final int DEFAULT_CHANNEL = 1;
    private static final int DEFAULT_AUTHORIZE_ID = 0;
    private static final int DEFAULT_DATE_TYPE = 1;
    private static final int DEFAULT_SELECT_INDEX = 0;

    private ApiService apiService;
    private MutableLiveData<EarningsResponse.EarningsItem> earningsData = new MutableLiveData<>();
    private MutableLiveData<Boolean> isLoading = new MutableLiveData<>();
    private MutableLiveData<String> error = new MutableLiveData<>();

    public EarningsRepository() {
        apiService = ApiClient.getApiService();
        isLoading.setValue(false);
    }

    public LiveData<EarningsResponse.EarningsItem> getEarningsData() {
        return earningsData;
    }

    public LiveData<Boolean> getIsLoading() {
        return isLoading;
    }

    public LiveData<String> getError() {
        return error;
    }

    public void fetchEarningsData(String authorization, String cookie) {
        isLoading.setValue(true);
        
        Call<EarningsResponse> call = apiService.getEarningsData(
                DEFAULT_CHANNEL,
                DEFAULT_VERSION,
                DEFAULT_AUTHORIZE_ID,
                DEFAULT_DATE_TYPE,
                DEFAULT_SELECT_INDEX,
                authorization,
                cookie
        );

        call.enqueue(new Callback<EarningsResponse>() {
            @Override
            public void onResponse(Call<EarningsResponse> call, Response<EarningsResponse> response) {
                isLoading.setValue(false);
                if (response.isSuccessful() && response.body() != null) {
                    EarningsResponse data = response.body();
                    if (data.getStatus() == 200 && data.getData() != null && 
                        data.getData().getList() != null && !data.getData().getList().isEmpty()) {
                        // Get the first item in the list as requested
                        earningsData.setValue(data.getData().getList().get(0));
                    } else {
                        error.setValue("数据获取失败: " + data.getMessage());
                    }
                } else {
                    error.setValue("响应失败: " + response.code());
                }
            }

            @Override
            public void onFailure(Call<EarningsResponse> call, Throwable t) {
                isLoading.setValue(false);
                error.setValue("网络请求失败: " + t.getMessage());
            }
        });
    }
} 