package com.example.myapplication.ui.login;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.webkit.ConsoleMessage;
import android.webkit.CookieManager;
import android.webkit.JavascriptInterface;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.appcompat.app.AppCompatActivity;

import com.example.myapplication.R;
import com.example.myapplication.data.SessionManager;
import com.example.myapplication.ui.main.MainActivity;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class LoginActivity extends AppCompatActivity {

    private WebView webView;
    private ImageView qrCodeImageView;
    private TextView tvStatus;
    private ProgressBar progressBar;
    private SessionManager sessionManager;
    private static final String LOGIN_URL = "https://www.goofish.pro/login";
    private static final String TAG = "LoginActivity";
    private String qrCodeImageUrl = null;
    private Timer loginCheckTimer;
    private boolean isCheckingLogin = false;
    private Handler mainHandler;
    private static final int MINIMUM_QR_IMAGE_SIZE = 10 * 1024; // 降低到10KB
    private static final int MINIMUM_QR_DIMENSION = 100; // 降低到100px
    private List<String> capturedImageUrls = new ArrayList<>(); // 存储所有捕获的图片URL

    @SuppressLint({"SetJavaScriptEnabled", "AddJavascriptInterface"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // 开启WebView调试
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }

        // 初始化组件
        sessionManager = new SessionManager(this);
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 获取UI组件
        webView = findViewById(R.id.webView);
        qrCodeImageView = findViewById(R.id.qrCodeImageView);
        tvStatus = findViewById(R.id.tvStatus);
        progressBar = findViewById(R.id.progressBar);
        
        // 检查是否已登录
        if (sessionManager.isLoggedIn()) {
            startMainActivity();
            return;
        }

        // 配置WebView
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        webSettings.setLoadsImagesAutomatically(true);
        webSettings.setCacheMode(WebSettings.LOAD_NO_CACHE);
        // webSettings.setAppCacheEnabled(false); // 已被弃用，删除此行
        webSettings.setBlockNetworkImage(false);
        webSettings.setBlockNetworkLoads(false);
        
        // 允许混合内容（http和https）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true);
        }
        
        // 清除所有cookies
        CookieManager.getInstance().removeAllCookies(null);
        CookieManager.getInstance().flush();
        
        // 清除缓存
        webView.clearCache(true);
        webView.clearHistory();

        // 添加JavaScript接口
        webView.addJavascriptInterface(new WebAppInterface(), "Android");

        // 设置WebChromeClient以便调试
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                Log.d(TAG, "Loading progress: " + newProgress + "%");
                
                // 当页面加载到80%时，尝试提取二维码
                if (newProgress > 80 && qrCodeImageUrl == null) {
                    extractQRCode();
                }
            }
            
            @Override
            public boolean onConsoleMessage(ConsoleMessage consoleMessage) {
                Log.d(TAG, "Console: " + consoleMessage.message());
                return super.onConsoleMessage(consoleMessage);
            }
        });

        // 设置WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d(TAG, "Page started loading: " + url);
                tvStatus.setText("正在加载登录页面...");
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "Page finished loading: " + url);
                tvStatus.setText("正在获取登录二维码...");
                
                // 给页面一点时间完全渲染
                mainHandler.postDelayed(() -> {
                    // 直接注入JavaScript寻找二维码
                    extractQRCode();
                    
                    // 如果还没找到，等几秒再试一次
                    if (qrCodeImageUrl == null) {
                        mainHandler.postDelayed(() -> extractQRCode(), 3000);
                    }
                    
                    // 注入JavaScript检测登录成功
                    checkLoginStatus();
                }, 1000);
            }
            
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                // 接受所有SSL证书
                handler.proceed();
            }
            
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                String url = "";
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                    url = request.getUrl().toString();
                }
                
                // 查找二维码图片（base64数据）
                if (url.startsWith("data:image") && qrCodeImageUrl == null) {
                    capturedImageUrls.add(url); // 保存所有图片URL以便分析
                    
                    Log.d(TAG, "Found potential image URL: " + url.substring(0, Math.min(50, url.length())) + "...");
                    
                    // 放宽条件，检查是否可能是二维码
                    if (isPossiblyQRCode(url)) {
                        Log.d(TAG, "Image passed filter, treating as QR code");
                        qrCodeImageUrl = url;
                        final String foundUrl = url;
                        mainHandler.post(() -> {
                            displayQRCode(foundUrl);
                        });
                    }
                }
                
                return super.shouldInterceptRequest(view, request);
            }
        });

        // 加载登录页面
        webView.loadUrl(LOGIN_URL);
        tvStatus.setText("正在加载...");
        
        // 设置返回键处理
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                setEnabled(false);
                getOnBackPressedDispatcher().onBackPressed();
            }
        });
        
        // 设置定时检查登录状态
        startLoginCheckTimer();
        
        // 添加重试按钮点击事件
        findViewById(R.id.btnRetry).setOnClickListener(v -> {
            // 清除缓存和状态
            webView.clearCache(true);
            webView.clearHistory();
            capturedImageUrls.clear();
            qrCodeImageUrl = null;
            
            // 重新加载
            progressBar.setVisibility(View.VISIBLE);
            tvStatus.setText("正在重新加载...");
            webView.loadUrl(LOGIN_URL);
            
            // 显示当前捕获的所有图片URL数量
            Toast.makeText(LoginActivity.this, "已捕获 " + capturedImageUrls.size() + " 个图像，正在重试", Toast.LENGTH_SHORT).show();
        });
        
        // 添加显示图片按钮点击事件
        findViewById(R.id.btnShowAllImages).setOnClickListener(v -> {
            if (capturedImageUrls.size() > 0) {
                // 直接尝试显示第一个图像
                displayQRCode(capturedImageUrls.get(0));
                Toast.makeText(LoginActivity.this, "显示图像 1/" + capturedImageUrls.size(), Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(LoginActivity.this, "未捕获到任何图像", Toast.LENGTH_SHORT).show();
            }
        });
    }
    
    // 判断是否可能是二维码，放宽条件
    private boolean isPossiblyQRCode(String dataUrl) {
        try {
            // 如果地址包含明显的QR字样，直接通过
            if (dataUrl.toLowerCase().contains("qr") || dataUrl.toLowerCase().contains("qrcode") || 
                dataUrl.toLowerCase().contains("login") || dataUrl.toLowerCase().contains("wx")) {
                return true;
            }
            
            // 过滤掉明显不是图片的数据
            if (!dataUrl.startsWith("data:image/")) {
                return false;
            }
            
            // 检查字符串长度，微信二维码通常很长
            if (dataUrl.length() < 500) { // 降低长度要求
                return false;
            }
            
            // 提取base64数据部分
            String base64Data = dataUrl.substring(dataUrl.indexOf(",") + 1);
            
            // 检查base64编码长度
            if (base64Data.length() < MINIMUM_QR_IMAGE_SIZE / 3) { // 降低大小要求
                return false;
            }
            
            // 尝试解码base64看是否是有效图像
            byte[] decodedBytes = Base64.decode(base64Data, Base64.DEFAULT);
            if (decodedBytes.length < MINIMUM_QR_IMAGE_SIZE) {
                return false;
            }
            
            // 解码图像尺寸
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length, options);
            
            // 记录图像信息
            Log.d(TAG, "Image dimensions: " + options.outWidth + "x" + options.outHeight + 
                  ", Size: " + decodedBytes.length + " bytes");
            
            // 检查尺寸
            if (options.outWidth < MINIMUM_QR_DIMENSION || options.outHeight < MINIMUM_QR_DIMENSION) {
                Log.d(TAG, "Image too small");
                return false;
            }
            
            // 检查宽高比(放宽条件)
            float ratio = (float) options.outWidth / options.outHeight;
            if (ratio < 0.5 || ratio > 2.0) {
                Log.d(TAG, "Image not square-like: ratio=" + ratio);
                return false;
            }
            
            // 通过所有检查
            return true;
            
        } catch (Exception e) {
            Log.e(TAG, "Error checking QR code: " + e.getMessage());
            return false;
        }
    }
    
    // 直接提取二维码的JavaScript
    private void extractQRCode() {
        if (qrCodeImageUrl != null) return; // 已经找到二维码
        
        Log.d(TAG, "Attempting to extract QR code via JavaScript");
        
        webView.evaluateJavascript(
            "(function() {" +
            "   console.log('Running QR code extraction');" +
            "   // 调试DOM结构" +
            "   console.log('DOM structure:', document.documentElement.innerHTML.substring(0, 1000));" +
            "   " +
            "   // 1. 尝试通过特定类名找到微信登录二维码" +
            "   const specificSelectors = [" +
            "       '.weixin-login-qrcode', '.wx-qrcode', '.login-qrcode', '.qr-code', '.qrcode'," +
            "       '.login-qr', '.wechat-qr', '#qrcode', '#weixinQrCode', '#wxQrCode'," +
            "       '[id*=\"qr\"]', '[id*=\"QR\"]', '[id*=\"Qr\"]', '[class*=\"qr\"]', '[class*=\"QR\"]', '[class*=\"Qr\"]'" +
            "   ];" +
            "   " +
            "   console.log('Searching for specific QR selectors');" +
            "   for (const selector of specificSelectors) {" +
            "       const elements = document.querySelectorAll(selector);" +
            "       console.log('Found ' + elements.length + ' elements for selector: ' + selector);" +
            "       " +
            "       if (elements && elements.length > 0) {" +
            "           for (let i = 0; i < elements.length; i++) {" +
            "               const element = elements[i];" +
            "               console.log('Found element with selector: ' + selector);" +
            "               " +
            "               // 检查元素内部或自身是否为图像" +
            "               if (element.tagName === 'IMG' && element.src) {" +
            "                   console.log('Element is an image with src');" +
            "                   if (element.src.startsWith('data:image/')) {" +
            "                       console.log('Found base64 image in element');" +
            "                       return element.src;" +
            "                   }" +
            "               }" +
            "               " +
            "               // 检查子元素" +
            "               const imgs = element.querySelectorAll('img');" +
            "               console.log('Found ' + imgs.length + ' img elements inside ' + selector);" +
            "               " +
            "               for (let j = 0; j < imgs.length; j++) {" +
            "                   if (imgs[j].src && imgs[j].src.startsWith('data:image/')) {" +
            "                       console.log('Found base64 image in child');" +
            "                       return imgs[j].src;" +
            "                   }" +
            "               }" +
            "               " +
            "               // 检查背景图像" +
            "               const bgImage = window.getComputedStyle(element).backgroundImage;" +
            "               if (bgImage && bgImage.includes('data:image/')) {" +
            "                   console.log('Found base64 background image');" +
            "                   const match = bgImage.match(/data:image\\/[^;]+;base64,[^\"']+/);" +
            "                   if (match) return match[0];" +
            "               }" +
            "           }" +
            "       }" +
            "   }" +
            "   " +
            "   // 2. 查找所有图像元素" +
            "   console.log('Searching all images');" +
            "   let images = document.querySelectorAll('img');" +
            "   console.log('Found ' + images.length + ' images');" +
            "   let candidates = [];" +
            "   " +
            "   // 遍历所有图像，找到可能的二维码" +
            "   for (let i = 0; i < images.length; i++) {" +
            "       const img = images[i];" +
            "       const src = img.src;" +
            "       " +
            "       if (src && src.startsWith('data:image/')) {" +
            "           // 检查图像大小（width和height）" +
            "           const width = img.naturalWidth || img.width;" +
            "           const height = img.naturalHeight || img.height;" +
            "           const size = width * height;" +
            "           " +
            "           console.log('Image ' + i + ': ' + width + 'x' + height + ', src length: ' + src.length);" +
            "           " +
            "           // 根据尺寸、URL包含的关键词等判断是否可能是二维码" +
            "           let score = 0;" +
            "           " +
            "           // 尺寸评分" +
            "           if (width >= 100 && height >= 100) score += 10;" +
            "           if (width >= 200 && height >= 200) score += 10;" +
            "           " +
            "           // 长宽比评分" +
            "           const ratio = width / height;" +
            "           if (ratio >= 0.8 && ratio <= 1.2) score += 20;" +
            "           " +
            "           // URL长度评分" +
            "           if (src.length > 1000) score += 5;" +
            "           if (src.length > 5000) score += 5;" +
            "           " +
            "           // 关键词评分" +
            "           if (img.alt && (img.alt.toLowerCase().includes('qr') || " +
            "               img.alt.toLowerCase().includes('code') || " +
            "               img.alt.toLowerCase().includes('登录') || " +
            "               img.alt.toLowerCase().includes('微信'))) {" +
            "               score += 15;" +
            "           }" +
            "           " +
            "           // ID/类名评分" +
            "           if (img.id && (img.id.toLowerCase().includes('qr') || img.id.toLowerCase().includes('code'))) {" +
            "               score += 15;" +
            "           }" +
            "           if (img.className && (img.className.toLowerCase().includes('qr') || img.className.toLowerCase().includes('code'))) {" +
            "               score += 15;" +
            "           }" +
            "           " +
            "           // 记录候选项" +
            "           candidates.push({src: src, score: score, width: width, height: height});" +
            "       }" +
            "   }" +
            "   " +
            "   // 3. 查找所有canvas元素，可能用于绘制二维码" +
            "   console.log('Searching canvas elements');" +
            "   const canvases = document.querySelectorAll('canvas');" +
            "   for (let i = 0; i < canvases.length; i++) {" +
            "       try {" +
            "           const canvas = canvases[i];" +
            "           const width = canvas.width;" +
            "           const height = canvas.height;" +
            "           const dataUrl = canvas.toDataURL('image/png');" +
            "           " +
            "           if (dataUrl && dataUrl.startsWith('data:image/')) {" +
            "               let score = 0;" +
            "               " +
            "               // 尺寸评分" +
            "               if (width >= 100 && height >= 100) score += 10;" +
            "               if (width >= 200 && height >= 200) score += 10;" +
            "               " +
            "               // 长宽比评分" +
            "               const ratio = width / height;" +
            "               if (ratio >= 0.8 && ratio <= 1.2) score += 20;" +
            "               " +
            "               // ID/类名评分" +
            "               if (canvas.id && (canvas.id.toLowerCase().includes('qr') || canvas.id.toLowerCase().includes('code'))) {" +
            "                   score += 15;" +
            "               }" +
            "               if (canvas.className && (canvas.className.toLowerCase().includes('qr') || canvas.className.toLowerCase().includes('code'))) {" +
            "                   score += 15;" +
            "               }" +
            "               " +
            "               // 记录候选项" +
            "               candidates.push({src: dataUrl, score: score, width: width, height: height});" +
            "           }" +
            "       } catch(e) { console.error('Canvas error:', e); }" +
            "   }" +
            "   " +
            "   // 4. 检查iframe中的内容" +
            "   console.log('Checking iframes');" +
            "   const iframes = document.querySelectorAll('iframe');" +
            "   for (let i = 0; i < iframes.length; i++) {" +
            "       try {" +
            "           const iframe = iframes[i];" +
            "           if (iframe.contentDocument) {" +
            "               const iframeImages = iframe.contentDocument.querySelectorAll('img');" +
            "               console.log('Found ' + iframeImages.length + ' images in iframe ' + i);" +
            "               " +
            "               for (let j = 0; j < iframeImages.length; j++) {" +
            "                   const img = iframeImages[j];" +
            "                   if (img.src && img.src.startsWith('data:image/')) {" +
            "                       const width = img.naturalWidth || img.width;" +
            "                       const height = img.naturalHeight || img.height;" +
            "                       let score = 0;" +
            "                       " +
            "                       if (width >= 100 && height >= 100) score += 10;" +
            "                       if (width >= 200 && height >= 200) score += 10;" +
            "                       " +
            "                       const ratio = width / height;" +
            "                       if (ratio >= 0.8 && ratio <= 1.2) score += 20;" +
            "                       " +
            "                       candidates.push({src: img.src, score: score, width: width, height: height});" +
            "                   }" +
            "               }" +
            "           }" +
            "       } catch(e) { console.error('Iframe error:', e); }" +
            "   }" +
            "   " +
            "   // 根据评分选择最佳候选项" +
            "   console.log('Sorting candidates');" +
            "   candidates.sort((a, b) => b.score - a.score);" +
            "   console.log('Candidates: ', candidates.length);" +
            "   " +
            "   if (candidates.length > 0) {" +
            "       console.log('Best candidate: score=' + candidates[0].score + ', size=' + candidates[0].width + 'x' + candidates[0].height);" +
            "       return candidates[0].src;" +
            "   }" +
            "   " +
            "   return '';" +
            "})();",
            value -> {
                if (value != null && !value.equals("\"\"") && !value.equals("null")) {
                    // 去除引号
                    String imageUrl = value.substring(1, value.length() - 1);
                    if (imageUrl.startsWith("data:image/") && qrCodeImageUrl == null) {
                        Log.d(TAG, "Found QR code via JavaScript: " + imageUrl.substring(0, Math.min(50, imageUrl.length())) + "...");
                        qrCodeImageUrl = imageUrl;
                        capturedImageUrls.add(imageUrl); // 保存URL
                        final String foundUrl = imageUrl;
                        runOnUiThread(() -> {
                            displayQRCode(foundUrl);
                        });
                    } else {
                        Log.d(TAG, "JavaScript returned non-image data or QR already found");
                    }
                } else {
                    Log.d(TAG, "No QR found via JavaScript");
                    // 没有找到二维码，但我们不立即重试，避免过多调用
                }
            }
        );
    }
    
    // 显示二维码
    private void displayQRCode(String dataUrl) {
        try {
            Log.d(TAG, "Displaying QR code from URL: " + dataUrl.substring(0, Math.min(50, dataUrl.length())) + "...");
            
            // 解析base64图像数据
            String base64Data;
            if (dataUrl.indexOf(",") != -1) {
                base64Data = dataUrl.substring(dataUrl.indexOf(",") + 1);
            } else {
                Log.e(TAG, "Invalid data URL format");
                tvStatus.setText("二维码格式错误");
                return;
            }
            
            byte[] decodedBytes = Base64.decode(base64Data, Base64.DEFAULT);
            Bitmap bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
            
            if (bitmap != null) {
                int width = bitmap.getWidth();
                int height = bitmap.getHeight();
                Log.d(TAG, "QR code dimensions: " + width + "x" + height);
                
                // 显示二维码
                qrCodeImageView.setImageBitmap(bitmap);
                progressBar.setVisibility(View.GONE);
                tvStatus.setText("请使用微信扫描上方二维码登录");
                Log.d(TAG, "QR code displayed successfully");
            } else {
                Log.e(TAG, "二维码bitmap为空");
                tvStatus.setText("二维码解析失败，请点击重试");
                progressBar.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            Log.e(TAG, "二维码显示失败: " + e.getMessage());
            tvStatus.setText("二维码加载失败，请点击重试");
            progressBar.setVisibility(View.GONE);
        }
    }
    
    // 设置定时检查登录状态
    private void startLoginCheckTimer() {
        // 取消之前的定时器（如果存在）
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
        }
        
        // 创建新的定时器
        loginCheckTimer = new Timer();
        loginCheckTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isCheckingLogin) {
                    checkLoginStatus();
                }
            }
        }, 3000, 3000); // 每3秒检查一次
    }
    
    // 检查登录状态
    private void checkLoginStatus() {
        if (isCheckingLogin) return;
        
        isCheckingLogin = true;
        
        webView.post(() -> {
            webView.evaluateJavascript(
                "(function() { " +
                "   if (document.cookie.indexOf('goofish_session') !== -1) { " +
                "       return document.cookie;" +
                "   }" +
                "   return '';" +
                "})();", value -> {
                    isCheckingLogin = false;
                    
                    if (value != null && !value.equals("\"\"") && !value.equals("null")) {
                        // 登录成功
                        String cookies = value.substring(1, value.length() - 1); // 去除引号
                        onLoginSuccess(cookies);
                    }
                }
            );
        });
    }
    
    // 登录成功处理
    private void onLoginSuccess(String cookies) {
        Log.d(TAG, "Login success detected");
        
        // 停止检查
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
        }
        
        // 保存cookies
        sessionManager.saveCookie(cookies);
        
        // 提取授权令牌
        if (cookies.contains("Authorization=")) {
            String[] parts = cookies.split(";");
            for (String part : parts) {
                if (part.trim().startsWith("Authorization=")) {
                    String token = part.trim().substring("Authorization=".length());
                    sessionManager.saveAuthToken(token);
                    break;
                }
            }
            // 完成登录流程
            completeLogin();
        } else {
            // 如果Cookie中没有授权令牌，尝试从头部获取
            webView.evaluateJavascript(
                    "fetch('https://api.goofish.pro/api/user/info')" +
                    ".then(response => {" +
                    "   const auth = response.headers.get('Authorization');" +
                    "   if (auth) {" +
                    "       return auth;" +
                    "   }" +
                    "   return '';" +
                    "});", token -> {
                        if (token != null && !token.equals("\"\"") && !token.equals("null")) {
                            String authToken = token.substring(1, token.length() - 1);
                            sessionManager.saveAuthToken(authToken);
                        }
                        completeLogin();
                    }
            );
        }
    }
    
    // 完成登录流程
    private void completeLogin() {
        // 标记为已登录
        sessionManager.setLoggedIn(true);
        
        // 启动主活动
        runOnUiThread(() -> {
            tvStatus.setText("登录成功，正在进入...");
            Toast.makeText(LoginActivity.this, "登录成功", Toast.LENGTH_SHORT).show();
            startMainActivity();
        });
    }
    
    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
            loginCheckTimer = null;
        }
    }
    
    // JavaScript接口
    private class WebAppInterface {
        @JavascriptInterface
        public void saveAuthToken(String token) {
            sessionManager.saveAuthToken(token);
        }
        
        @JavascriptInterface
        public void logMessage(String message) {
            Log.d(TAG, "JS Log: " + message);
        }
        
        @JavascriptInterface
        public void foundQRCode(String qrData) {
            if (qrCodeImageUrl == null && qrData != null && qrData.startsWith("data:image/")) {
                Log.d(TAG, "QR Code found via JS interface");
                qrCodeImageUrl = qrData;
                final String data = qrData;
                runOnUiThread(() -> {
                    displayQRCode(data);
                });
            }
        }
    }
} 