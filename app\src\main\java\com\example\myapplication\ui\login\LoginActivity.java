package com.example.myapplication.ui.login;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.JavascriptInterface;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.appcompat.app.AppCompatActivity;

import com.example.myapplication.R;
import com.example.myapplication.data.SessionManager;
import com.example.myapplication.ui.main.MainActivity;

import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class LoginActivity extends AppCompatActivity {

    private WebView webView;
    private ImageView qrCodeImageView;
    private TextView tvStatus;
    private ProgressBar progressBar;
    private SessionManager sessionManager;
    private static final String LOGIN_URL = "https://www.goofish.pro/login";
    private static final String TAG = "LoginActivity";
    private String qrCodeImageUrl = null;
    private int qrSearchAttempts = 0;
    private static final int MAX_QR_SEARCH_ATTEMPTS = 10;
    private Timer loginCheckTimer;
    private boolean isCheckingLogin = false;
    private Handler mainHandler;

    @SuppressLint({"SetJavaScriptEnabled", "AddJavascriptInterface"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // 开启WebView调试
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }

        // 初始化组件
        sessionManager = new SessionManager(this);
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 获取UI组件
        webView = findViewById(R.id.webView);
        qrCodeImageView = findViewById(R.id.qrCodeImageView);
        tvStatus = findViewById(R.id.tvStatus);
        progressBar = findViewById(R.id.progressBar);
        
        // 检查是否已登录
        if (sessionManager.isLoggedIn()) {
            startMainActivity();
            return;
        }

        // 配置WebView
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        
        // 允许混合内容（http和https）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true);
        }
        
        // 清除所有cookies
        CookieManager.getInstance().removeAllCookies(null);
        CookieManager.getInstance().flush();

        // 添加JavaScript接口
        webView.addJavascriptInterface(new WebAppInterface(), "Android");

        // 设置WebChromeClient以便调试
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                Log.d(TAG, "Loading progress: " + newProgress + "%");
            }
        });

        // 设置WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d(TAG, "Page started loading: " + url);
                tvStatus.setText("正在加载登录页面...");
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "Page finished loading: " + url);
                tvStatus.setText("正在获取登录二维码...");
                
                // 等待页面稳定后再搜索二维码
                mainHandler.postDelayed(() -> {
                    // 注入网络监听和DOM监听
                    injectQRCodeMonitoring();
                    // 直接搜索二维码
                    extractQRCode();

                    // 如果还没找到，使用强化搜索
                    mainHandler.postDelayed(() -> {
                        if (qrCodeImageUrl == null) {
                            Log.d(TAG, "Regular search failed, trying aggressive search...");
                            searchForQRCodeAggressively();
                        }
                    }, 3000);
                }, 1000);

                // 注入JavaScript检测登录成功
                checkLoginStatus();
            }
            
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                // 接受所有SSL证书
                handler.proceed();
            }
            
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                String url = "";
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                    url = request.getUrl().toString();
                }

                // 记录所有请求以便调试
                if (url.contains("qr") || url.contains("code") || url.contains("login")) {
                    Log.d(TAG, "Login related request: " + url);
                }

                // 查找二维码相关的API请求
                if (url.contains("/qr") || url.contains("/code") || url.contains("qrcode")) {
                    Log.d(TAG, "QR code API request detected: " + url);
                }

                // 查找二维码图片（base64数据）- 但要更谨慎地处理
                if (url.startsWith("data:image/png;base64") && qrCodeImageUrl == null) {
                    // 检查URL长度，二维码通常比较大
                    if (url.length() > 1000) { // 二维码base64通常很长
                        Log.d(TAG, "Found potential QR code image URL (length: " + url.length() + "): " + url.substring(0, 50) + "...");
                        final String foundUrl = url;
                        mainHandler.post(() -> {
                            displayQRCode(foundUrl);
                        });
                        qrCodeImageUrl = url;
                    } else {
                        Log.d(TAG, "Skipped small base64 image (length: " + url.length() + "): " + url.substring(0, Math.min(50, url.length())) + "...");
                    }
                }

                return super.shouldInterceptRequest(view, request);
            }
        });

        // 加载登录页面
        webView.loadUrl(LOGIN_URL);
        tvStatus.setText("正在加载...");
        
        // 设置返回键处理
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                setEnabled(false);
                getOnBackPressedDispatcher().onBackPressed();
            }
        });
        
        // 设置定时检查登录状态
        startLoginCheckTimer();
    }

    // 注入二维码监听JavaScript
    private void injectQRCodeMonitoring() {
        webView.evaluateJavascript(
            "(function() {" +
            "   console.log('注入二维码监听器...');" +
            "   " +
            "   // 监听网络请求" +
            "   if (window.XMLHttpRequest) {" +
            "       const originalOpen = XMLHttpRequest.prototype.open;" +
            "       XMLHttpRequest.prototype.open = function(method, url, async, user, password) {" +
            "           if (url.includes('qr') || url.includes('code') || url.includes('login')) {" +
            "               console.log('XHR请求:', method, url);" +
            "           }" +
            "           return originalOpen.apply(this, arguments);" +
            "       };" +
            "   }" +
            "   " +
            "   // 监听fetch请求" +
            "   if (window.fetch) {" +
            "       const originalFetch = window.fetch;" +
            "       window.fetch = function(input, init) {" +
            "           const url = typeof input === 'string' ? input : input.url;" +
            "           if (url.includes('qr') || url.includes('code') || url.includes('login')) {" +
            "               console.log('Fetch请求:', url);" +
            "           }" +
            "           return originalFetch.apply(this, arguments);" +
            "       };" +
            "   }" +
            "   " +
            "   // 监听DOM变化" +
            "   if (window.MutationObserver) {" +
            "       const observer = new MutationObserver(function(mutations) {" +
            "           mutations.forEach(function(mutation) {" +
            "               if (mutation.type === 'childList') {" +
            "                   mutation.addedNodes.forEach(function(node) {" +
            "                       if (node.nodeType === 1) {" + // Element node
            "                           // 检查新添加的图片元素" +
            "                           if (node.tagName === 'IMG' && node.src && node.src.startsWith('data:image/png;base64')) {" +
            "                               console.log('检测到新的base64图片:', node.src.substring(0, 50));" +
            "                               Android.onQRCodeFound(node.src);" +
            "                           }" +
            "                           // 检查新添加元素的子图片" +
            "                           const imgs = node.querySelectorAll ? node.querySelectorAll('img') : [];" +
            "                           for (let i = 0; i < imgs.length; i++) {" +
            "                               if (imgs[i].src && imgs[i].src.startsWith('data:image/png;base64')) {" +
            "                                   console.log('检测到新的子图片:', imgs[i].src.substring(0, 50));" +
            "                                   Android.onQRCodeFound(imgs[i].src);" +
            "                               }" +
            "                           }" +
            "                       }" +
            "                   });" +
            "               }" +
            "           });" +
            "       });" +
            "       observer.observe(document.body, { childList: true, subtree: true });" +
            "       console.log('DOM监听器已启动');" +
            "   }" +
            "   " +
            "   return 'QR监听器注入完成';" +
            "})();",
            result -> Log.d(TAG, "QR monitoring injection result: " + result)
        );
    }
    
    // 直接提取二维码的JavaScript
    private void extractQRCode() {
        webView.evaluateJavascript(
            "(function() {" +
            "   console.log('开始搜索二维码...');" +
            "   let qrCodeUrl = '';" +
            "   " +
            "   // 方法1: 查找包含二维码的特定元素" +
            "   let qrElements = document.querySelectorAll('[class*=\"qr\"], [id*=\"qr\"], [class*=\"code\"], [id*=\"code\"]');" +
            "   for(let i=0; i<qrElements.length; i++) {" +
            "       let element = qrElements[i];" +
            "       if(element.tagName === 'IMG' && element.src && element.src.startsWith('data:image/png;base64')) {" +
            "           console.log('找到二维码元素(方法1):', element.className, element.id);" +
            "           qrCodeUrl = element.src;" +
            "           break;" +
            "       }" +
            "       // 检查子元素中的图片" +
            "       let imgs = element.querySelectorAll('img');" +
            "       for(let j=0; j<imgs.length; j++) {" +
            "           if(imgs[j].src && imgs[j].src.startsWith('data:image/png;base64')) {" +
            "               console.log('找到二维码子元素(方法1):', imgs[j].className, imgs[j].id);" +
            "               qrCodeUrl = imgs[j].src;" +
            "               break;" +
            "           }" +
            "       }" +
            "       if(qrCodeUrl) break;" +
            "   }" +
            "   " +
            "   // 方法2: 如果方法1没找到，查找所有base64图片并过滤" +
            "   if(!qrCodeUrl) {" +
            "       let allImages = document.querySelectorAll('img');" +
            "       let base64Images = [];" +
            "       for(let i=0; i<allImages.length; i++) {" +
            "           let src = allImages[i].src;" +
            "           if(src && src.startsWith('data:image/png;base64')) {" +
            "               let imgInfo = {" +
            "                   src: src," +
            "                   width: allImages[i].width || allImages[i].naturalWidth," +
            "                   height: allImages[i].height || allImages[i].naturalHeight," +
            "                   className: allImages[i].className," +
            "                   id: allImages[i].id," +
            "                   alt: allImages[i].alt" +
            "               };" +
            "               base64Images.push(imgInfo);" +
            "               console.log('发现base64图片:', imgInfo.width + 'x' + imgInfo.height, imgInfo.className, imgInfo.id, imgInfo.alt);" +
            "           }" +
            "       }" +
            "       " +
            "       // 优先选择正方形或接近正方形的图片（二维码通常是正方形）" +
            "       for(let i=0; i<base64Images.length; i++) {" +
            "           let img = base64Images[i];" +
            "           let ratio = img.width / img.height;" +
            "           // 二维码通常是正方形，宽高比接近1，且尺寸合理（通常大于100px）" +
            "           if(ratio >= 0.8 && ratio <= 1.2 && img.width >= 100 && img.height >= 100) {" +
            "               console.log('选择二维码候选:', img.width + 'x' + img.height, img.className, img.id);" +
            "               qrCodeUrl = img.src;" +
            "               break;" +
            "           }" +
            "       }" +
            "       " +
            "       // 如果还没找到，选择最大的base64图片" +
            "       if(!qrCodeUrl && base64Images.length > 0) {" +
            "           let largestImg = base64Images[0];" +
            "           for(let i=1; i<base64Images.length; i++) {" +
            "               if(base64Images[i].width * base64Images[i].height > largestImg.width * largestImg.height) {" +
            "                   largestImg = base64Images[i];" +
            "               }" +
            "           }" +
            "           console.log('选择最大图片作为二维码:', largestImg.width + 'x' + largestImg.height);" +
            "           qrCodeUrl = largestImg.src;" +
            "       }" +
            "   }" +
            "   " +
            "   console.log('二维码搜索结果:', qrCodeUrl ? '找到' : '未找到');" +
            "   return qrCodeUrl;" +
            "})();",
            value -> {
                if (value != null && !value.equals("\"\"") && !value.equals("null")) {
                    // 去除引号
                    String imageUrl = value.substring(1, value.length() - 1);
                    if (imageUrl.startsWith("data:image/png;base64") && qrCodeImageUrl == null) {
                        Log.d(TAG, "Found QR code via JavaScript: " + imageUrl.substring(0, 50) + "...");
                        qrCodeImageUrl = imageUrl;
                        final String foundUrl = imageUrl;
                        runOnUiThread(() -> {
                            displayQRCode(foundUrl);
                        });
                    }
                } else {
                    // 没有找到二维码，设置定时重试
                    qrSearchAttempts++;
                    if (qrSearchAttempts < MAX_QR_SEARCH_ATTEMPTS) {
                        Log.d(TAG, "QR code not found (attempt " + qrSearchAttempts + "/" + MAX_QR_SEARCH_ATTEMPTS + "), retrying in 2 seconds...");
                        mainHandler.postDelayed(this::extractQRCode, 2000);
                    } else {
                        Log.w(TAG, "Max QR search attempts reached, trying aggressive search...");
                        searchForQRCodeAggressively();
                    }
                }
            }
        );
    }

    // 强化的二维码搜索方法
    private void searchForQRCodeAggressively() {
        webView.evaluateJavascript(
            "(function() {" +
            "   console.log('开始强化二维码搜索...');" +
            "   " +
            "   // 搜索所有可能的二维码容器" +
            "   const selectors = [" +
            "       'img[src*=\"data:image/png;base64\"]'," +
            "       '[class*=\"qr\"] img'," +
            "       '[id*=\"qr\"] img'," +
            "       '[class*=\"code\"] img'," +
            "       '[id*=\"code\"] img'," +
            "       '[class*=\"login\"] img'," +
            "       '[id*=\"login\"] img'," +
            "       'canvas'," +
            "       'svg'" +
            "   ];" +
            "   " +
            "   let foundImages = [];" +
            "   " +
            "   selectors.forEach(selector => {" +
            "       try {" +
            "           const elements = document.querySelectorAll(selector);" +
            "           elements.forEach(el => {" +
            "               if (el.tagName === 'IMG' && el.src && el.src.startsWith('data:image/png;base64')) {" +
            "                   foundImages.push({" +
            "                       src: el.src," +
            "                       width: el.width || el.naturalWidth || 0," +
            "                       height: el.height || el.naturalHeight || 0," +
            "                       className: el.className," +
            "                       id: el.id," +
            "                       selector: selector" +
            "                   });" +
            "               } else if (el.tagName === 'CANVAS') {" +
            "                   try {" +
            "                       const dataUrl = el.toDataURL('image/png');" +
            "                       if (dataUrl && dataUrl.startsWith('data:image/png;base64')) {" +
            "                           foundImages.push({" +
            "                               src: dataUrl," +
            "                               width: el.width," +
            "                               height: el.height," +
            "                               className: el.className," +
            "                               id: el.id," +
            "                               selector: selector + ' (canvas)'" +
            "                           });" +
            "                       }" +
            "                   } catch(e) {" +
            "                       console.log('Canvas转换失败:', e);" +
            "                   }" +
            "               }" +
            "           });" +
            "       } catch(e) {" +
            "           console.log('选择器错误:', selector, e);" +
            "       }" +
            "   });" +
            "   " +
            "   console.log('找到', foundImages.length, '个候选图片');" +
            "   " +
            "   // 按优先级排序：正方形、大尺寸、包含qr/code关键词" +
            "   foundImages.sort((a, b) => {" +
            "       const aScore = getQRScore(a);" +
            "       const bScore = getQRScore(b);" +
            "       return bScore - aScore;" +
            "   });" +
            "   " +
            "   function getQRScore(img) {" +
            "       let score = 0;" +
            "       const ratio = img.width / img.height;" +
            "       " +
            "       // 正方形加分" +
            "       if (ratio >= 0.8 && ratio <= 1.2) score += 50;" +
            "       " +
            "       // 尺寸加分" +
            "       if (img.width >= 100 && img.height >= 100) score += 30;" +
            "       if (img.width >= 200 && img.height >= 200) score += 20;" +
            "       " +
            "       // 关键词加分" +
            "       const text = (img.className + ' ' + img.id + ' ' + img.selector).toLowerCase();" +
            "       if (text.includes('qr')) score += 40;" +
            "       if (text.includes('code')) score += 30;" +
            "       if (text.includes('login')) score += 20;" +
            "       " +
            "       // 数据长度加分（二维码通常数据量大）" +
            "       if (img.src.length > 5000) score += 25;" +
            "       if (img.src.length > 10000) score += 25;" +
            "       " +
            "       return score;" +
            "   }" +
            "   " +
            "   if (foundImages.length > 0) {" +
            "       const bestImage = foundImages[0];" +
            "       console.log('选择最佳候选:', bestImage.width + 'x' + bestImage.height, bestImage.className, bestImage.id);" +
            "       return bestImage.src;" +
            "   }" +
            "   " +
            "   return '';" +
            "})();",
            value -> {
                if (value != null && !value.equals("\"\"") && !value.equals("null")) {
                    String imageUrl = value.substring(1, value.length() - 1);
                    if (imageUrl.startsWith("data:image/png;base64") && qrCodeImageUrl == null) {
                        Log.d(TAG, "Found QR code via aggressive search: " + imageUrl.substring(0, 50) + "...");
                        qrCodeImageUrl = imageUrl;
                        runOnUiThread(() -> {
                            displayQRCode(imageUrl);
                        });
                    }
                } else {
                    Log.d(TAG, "Aggressive search found no QR code");
                }
            }
        );
    }
    
    // 显示二维码
    private void displayQRCode(String dataUrl) {
        try {
            // 解析base64图像数据
            String base64Data = dataUrl.substring(dataUrl.indexOf(",") + 1);
            byte[] decodedBytes = Base64.decode(base64Data, Base64.DEFAULT);
            Bitmap bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);

            if (bitmap != null) {
                // 检查图片尺寸，确保是合理的二维码大小
                int width = bitmap.getWidth();
                int height = bitmap.getHeight();
                Log.d(TAG, "QR code bitmap size: " + width + "x" + height);

                // 二维码通常是正方形且有一定尺寸
                if (width >= 50 && height >= 50) {
                    // 显示二维码
                    qrCodeImageView.setImageBitmap(bitmap);
                    progressBar.setVisibility(View.GONE);
                    tvStatus.setText("请使用微信扫描上方二维码登录");
                    Log.d(TAG, "QR code displayed successfully: " + width + "x" + height);
                } else {
                    Log.w(TAG, "图片尺寸太小，可能不是二维码: " + width + "x" + height);
                    // 重置搜索状态，继续寻找
                    qrCodeImageUrl = null;
                    tvStatus.setText("正在寻找二维码...");
                    mainHandler.postDelayed(this::extractQRCode, 1000);
                }
            } else {
                Log.e(TAG, "二维码bitmap为空");
                tvStatus.setText("二维码加载失败，正在重试...");
                // 重置搜索状态
                qrCodeImageUrl = null;
                mainHandler.postDelayed(this::extractQRCode, 2000);
            }
        } catch (Exception e) {
            Log.e(TAG, "二维码显示失败: " + e.getMessage());
            tvStatus.setText("二维码加载失败，正在重试...");
            // 重置搜索状态
            qrCodeImageUrl = null;
            mainHandler.postDelayed(this::extractQRCode, 2000);
        }
    }

    // 重置二维码搜索状态
    private void resetQRCodeSearch() {
        qrCodeImageUrl = null;
        qrSearchAttempts = 0;
        tvStatus.setText("正在重新搜索二维码...");
        extractQRCode();
    }
    
    // 设置定时检查登录状态
    private void startLoginCheckTimer() {
        // 取消之前的定时器（如果存在）
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
        }
        
        // 创建新的定时器
        loginCheckTimer = new Timer();
        loginCheckTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isCheckingLogin) {
                    checkLoginStatus();
                }
            }
        }, 3000, 3000); // 每3秒检查一次
    }
    
    // 检查登录状态
    private void checkLoginStatus() {
        if (isCheckingLogin) return;
        
        isCheckingLogin = true;
        
        webView.post(() -> {
            webView.evaluateJavascript(
                "(function() { " +
                "   if (document.cookie.indexOf('goofish_session') !== -1) { " +
                "       return document.cookie;" +
                "   }" +
                "   return '';" +
                "})();", value -> {
                    isCheckingLogin = false;
                    
                    if (value != null && !value.equals("\"\"") && !value.equals("null")) {
                        // 登录成功
                        String cookies = value.substring(1, value.length() - 1); // 去除引号
                        onLoginSuccess(cookies);
                    }
                }
            );
        });
    }
    
    // 登录成功处理
    private void onLoginSuccess(String cookies) {
        Log.d(TAG, "Login success detected");
        
        // 停止检查
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
        }
        
        // 保存cookies
        sessionManager.saveCookie(cookies);
        
        // 提取授权令牌
        if (cookies.contains("Authorization=")) {
            String[] parts = cookies.split(";");
            for (String part : parts) {
                if (part.trim().startsWith("Authorization=")) {
                    String token = part.trim().substring("Authorization=".length());
                    sessionManager.saveAuthToken(token);
                    break;
                }
            }
            // 完成登录流程
            completeLogin();
        } else {
            // 如果Cookie中没有授权令牌，尝试从头部获取
            webView.evaluateJavascript(
                    "fetch('https://api.goofish.pro/api/user/info')" +
                    ".then(response => {" +
                    "   const auth = response.headers.get('Authorization');" +
                    "   if (auth) {" +
                    "       return auth;" +
                    "   }" +
                    "   return '';" +
                    "});", token -> {
                        if (token != null && !token.equals("\"\"") && !token.equals("null")) {
                            String authToken = token.substring(1, token.length() - 1);
                            sessionManager.saveAuthToken(authToken);
                        }
                        completeLogin();
                    }
            );
        }
    }
    
    // 完成登录流程
    private void completeLogin() {
        // 标记为已登录
        sessionManager.setLoggedIn(true);
        
        // 启动主活动
        runOnUiThread(() -> {
            tvStatus.setText("登录成功，正在进入...");
            Toast.makeText(LoginActivity.this, "登录成功", Toast.LENGTH_SHORT).show();
            startMainActivity();
        });
    }
    
    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
            loginCheckTimer = null;
        }
    }
    
    // JavaScript接口
    private class WebAppInterface {
        @JavascriptInterface
        public void saveAuthToken(String token) {
            sessionManager.saveAuthToken(token);
        }

        @JavascriptInterface
        public void onQRCodeFound(String imageUrl) {
            Log.d(TAG, "JavaScript found QR code: " + imageUrl.substring(0, Math.min(50, imageUrl.length())) + "...");
            if (imageUrl.startsWith("data:image/png;base64") && qrCodeImageUrl == null) {
                // 检查图片大小，二维码通常比较大
                if (imageUrl.length() > 1000) {
                    qrCodeImageUrl = imageUrl;
                    runOnUiThread(() -> {
                        displayQRCode(imageUrl);
                    });
                } else {
                    Log.d(TAG, "Skipped small image (length: " + imageUrl.length() + ")");
                }
            }
        }
    }
} 