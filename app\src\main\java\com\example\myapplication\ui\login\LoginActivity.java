package com.example.myapplication.ui.login;

import android.annotation.SuppressLint;
import android.content.Intent;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.net.http.SslError;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Base64;
import android.util.Log;
import android.view.View;
import android.webkit.CookieManager;
import android.webkit.JavascriptInterface;
import android.webkit.SslErrorHandler;
import android.webkit.WebChromeClient;
import android.webkit.WebResourceRequest;
import android.webkit.WebResourceResponse;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.appcompat.app.AppCompatActivity;

import com.example.myapplication.R;
import com.example.myapplication.data.SessionManager;
import com.example.myapplication.ui.main.MainActivity;

import java.util.HashMap;
import java.util.Map;
import java.util.Timer;
import java.util.TimerTask;

public class LoginActivity extends AppCompatActivity {

    private WebView webView;
    private ImageView qrCodeImageView;
    private TextView tvStatus;
    private ProgressBar progressBar;
    private SessionManager sessionManager;
    private static final String LOGIN_URL = "https://www.goofish.pro/login";
    private static final String TAG = "LoginActivity";
    private String qrCodeImageUrl = null;
    private Timer loginCheckTimer;
    private boolean isCheckingLogin = false;
    private Handler mainHandler;

    @SuppressLint({"SetJavaScriptEnabled", "AddJavascriptInterface"})
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_login);

        // 开启WebView调试
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            WebView.setWebContentsDebuggingEnabled(true);
        }

        // 初始化组件
        sessionManager = new SessionManager(this);
        mainHandler = new Handler(Looper.getMainLooper());
        
        // 获取UI组件
        webView = findViewById(R.id.webView);
        qrCodeImageView = findViewById(R.id.qrCodeImageView);
        tvStatus = findViewById(R.id.tvStatus);
        progressBar = findViewById(R.id.progressBar);
        
        // 检查是否已登录
        if (sessionManager.isLoggedIn()) {
            startMainActivity();
            return;
        }

        // 配置WebView
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webSettings.setJavaScriptCanOpenWindowsAutomatically(true);
        webSettings.setAllowFileAccess(true);
        webSettings.setUseWideViewPort(true);
        webSettings.setLoadWithOverviewMode(true);
        
        // 允许混合内容（http和https）
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            webSettings.setMixedContentMode(WebSettings.MIXED_CONTENT_ALWAYS_ALLOW);
            CookieManager.getInstance().setAcceptThirdPartyCookies(webView, true);
        }
        
        // 清除所有cookies
        CookieManager.getInstance().removeAllCookies(null);
        CookieManager.getInstance().flush();

        // 添加JavaScript接口
        webView.addJavascriptInterface(new WebAppInterface(), "Android");

        // 设置WebChromeClient以便调试
        webView.setWebChromeClient(new WebChromeClient() {
            @Override
            public void onProgressChanged(WebView view, int newProgress) {
                super.onProgressChanged(view, newProgress);
                Log.d(TAG, "Loading progress: " + newProgress + "%");
            }
        });

        // 设置WebViewClient
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                super.onPageStarted(view, url, favicon);
                Log.d(TAG, "Page started loading: " + url);
                tvStatus.setText("正在加载登录页面...");
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "Page finished loading: " + url);
                tvStatus.setText("正在获取登录二维码...");
                
                // 直接注入JavaScript寻找二维码
                extractQRCode();
                
                // 注入JavaScript检测登录成功
                checkLoginStatus();
            }
            
            @Override
            public void onReceivedSslError(WebView view, SslErrorHandler handler, SslError error) {
                // 接受所有SSL证书
                handler.proceed();
            }
            
            @Override
            public WebResourceResponse shouldInterceptRequest(WebView view, WebResourceRequest request) {
                String url = "";
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.LOLLIPOP) {
                    url = request.getUrl().toString();
                }
                
                // 查找二维码图片（base64数据）
                if (url.startsWith("data:image/png;base64") && qrCodeImageUrl == null) {
                    Log.d(TAG, "Found QR code image URL: " + url.substring(0, 50) + "...");
                    final String foundUrl = url;
                    mainHandler.post(() -> {
                        displayQRCode(foundUrl);
                    });
                    qrCodeImageUrl = url;
                }
                
                return super.shouldInterceptRequest(view, request);
            }
        });

        // 加载登录页面
        webView.loadUrl(LOGIN_URL);
        tvStatus.setText("正在加载...");
        
        // 设置返回键处理
        getOnBackPressedDispatcher().addCallback(this, new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                setEnabled(false);
                getOnBackPressedDispatcher().onBackPressed();
            }
        });
        
        // 设置定时检查登录状态
        startLoginCheckTimer();
    }
    
    // 直接提取二维码的JavaScript
    private void extractQRCode() {
        webView.evaluateJavascript(
            "(function() {" +
            "   let images = document.querySelectorAll('img');" +
            "   for(let i=0; i<images.length; i++) {" +
            "       let src = images[i].src;" +
            "       if(src && src.startsWith('data:image/png;base64')) {" +
            "           return src;" +
            "       }" +
            "   }" +
            "   return '';" +
            "})();",
            value -> {
                if (value != null && !value.equals("\"\"") && !value.equals("null")) {
                    // 去除引号
                    String imageUrl = value.substring(1, value.length() - 1);
                    if (imageUrl.startsWith("data:image/png;base64") && qrCodeImageUrl == null) {
                        Log.d(TAG, "Found QR code via JavaScript: " + imageUrl.substring(0, 50) + "...");
                        qrCodeImageUrl = imageUrl;
                        final String foundUrl = imageUrl;
                        runOnUiThread(() -> {
                            displayQRCode(foundUrl);
                        });
                    }
                } else {
                    // 没有找到二维码，设置定时重试
                    mainHandler.postDelayed(this::extractQRCode, 2000);
                }
            }
        );
    }
    
    // 显示二维码
    private void displayQRCode(String dataUrl) {
        try {
            // 解析base64图像数据
            String base64Data = dataUrl.substring(dataUrl.indexOf(",") + 1);
            byte[] decodedBytes = Base64.decode(base64Data, Base64.DEFAULT);
            Bitmap bitmap = BitmapFactory.decodeByteArray(decodedBytes, 0, decodedBytes.length);
            
            if (bitmap != null) {
                // 显示二维码
                qrCodeImageView.setImageBitmap(bitmap);
                progressBar.setVisibility(View.GONE);
                tvStatus.setText("请使用微信扫描上方二维码登录");
                Log.d(TAG, "QR code displayed successfully");
            } else {
                Log.e(TAG, "二维码bitmap为空");
                tvStatus.setText("二维码加载失败，请重试");
                progressBar.setVisibility(View.GONE);
            }
        } catch (Exception e) {
            Log.e(TAG, "二维码显示失败: " + e.getMessage());
            tvStatus.setText("二维码加载失败，请重试");
            progressBar.setVisibility(View.GONE);
        }
    }
    
    // 设置定时检查登录状态
    private void startLoginCheckTimer() {
        // 取消之前的定时器（如果存在）
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
        }
        
        // 创建新的定时器
        loginCheckTimer = new Timer();
        loginCheckTimer.schedule(new TimerTask() {
            @Override
            public void run() {
                if (!isCheckingLogin) {
                    checkLoginStatus();
                }
            }
        }, 3000, 3000); // 每3秒检查一次
    }
    
    // 检查登录状态
    private void checkLoginStatus() {
        if (isCheckingLogin) return;
        
        isCheckingLogin = true;
        
        webView.post(() -> {
            webView.evaluateJavascript(
                "(function() { " +
                "   if (document.cookie.indexOf('goofish_session') !== -1) { " +
                "       return document.cookie;" +
                "   }" +
                "   return '';" +
                "})();", value -> {
                    isCheckingLogin = false;
                    
                    if (value != null && !value.equals("\"\"") && !value.equals("null")) {
                        // 登录成功
                        String cookies = value.substring(1, value.length() - 1); // 去除引号
                        onLoginSuccess(cookies);
                    }
                }
            );
        });
    }
    
    // 登录成功处理
    private void onLoginSuccess(String cookies) {
        Log.d(TAG, "Login success detected");
        
        // 停止检查
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
        }
        
        // 保存cookies
        sessionManager.saveCookie(cookies);
        
        // 提取授权令牌
        if (cookies.contains("Authorization=")) {
            String[] parts = cookies.split(";");
            for (String part : parts) {
                if (part.trim().startsWith("Authorization=")) {
                    String token = part.trim().substring("Authorization=".length());
                    sessionManager.saveAuthToken(token);
                    break;
                }
            }
            // 完成登录流程
            completeLogin();
        } else {
            // 如果Cookie中没有授权令牌，尝试从头部获取
            webView.evaluateJavascript(
                    "fetch('https://api.goofish.pro/api/user/info')" +
                    ".then(response => {" +
                    "   const auth = response.headers.get('Authorization');" +
                    "   if (auth) {" +
                    "       return auth;" +
                    "   }" +
                    "   return '';" +
                    "});", token -> {
                        if (token != null && !token.equals("\"\"") && !token.equals("null")) {
                            String authToken = token.substring(1, token.length() - 1);
                            sessionManager.saveAuthToken(authToken);
                        }
                        completeLogin();
                    }
            );
        }
    }
    
    // 完成登录流程
    private void completeLogin() {
        // 标记为已登录
        sessionManager.setLoggedIn(true);
        
        // 启动主活动
        runOnUiThread(() -> {
            tvStatus.setText("登录成功，正在进入...");
            Toast.makeText(LoginActivity.this, "登录成功", Toast.LENGTH_SHORT).show();
            startMainActivity();
        });
    }
    
    private void startMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        startActivity(intent);
        finish();
    }
    
    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (loginCheckTimer != null) {
            loginCheckTimer.cancel();
            loginCheckTimer = null;
        }
    }
    
    // JavaScript接口
    private class WebAppInterface {
        @JavascriptInterface
        public void saveAuthToken(String token) {
            sessionManager.saveAuthToken(token);
        }
    }
} 