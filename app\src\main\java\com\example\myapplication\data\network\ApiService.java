package com.example.myapplication.data.network;

import com.example.myapplication.data.model.EarningsResponse;

import retrofit2.Call;
import retrofit2.http.GET;
import retrofit2.http.Header;
import retrofit2.http.Query;

public interface ApiService {
    
    @GET("/api/stats/order")
    Call<EarningsResponse> getEarningsData(
            @Query("channel") int channel,
            @Query("version") String version,
            @Query("authorize_id") int authorizeId,
            @Query("date_type") int dateType,
            @Query("selectIndex") int selectIndex,
            @Header("Authorization") String authorization,
            @Header("Cookie") String cookie
    );
} 