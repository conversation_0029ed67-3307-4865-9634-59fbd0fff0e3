package com.example.myapplication.ui.main;

import android.app.Application;
import android.os.Handler;
import android.os.Looper;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.example.myapplication.data.SessionManager;
import com.example.myapplication.data.model.EarningsResponse;
import com.example.myapplication.data.repository.EarningsRepository;

public class MainViewModel extends AndroidViewModel {

    private EarningsRepository repository;
    private SessionManager sessionManager;
    private Handler handler;
    private static final long REFRESH_INTERVAL = 10000; // 10 seconds
    private MutableLiveData<Boolean> isRefreshing = new MutableLiveData<>();
    private boolean autoRefreshEnabled = false;

    public MainViewModel(@NonNull Application application) {
        super(application);
        repository = new EarningsRepository();
        sessionManager = new SessionManager(application);
        handler = new Handler(Looper.getMainLooper());
        isRefreshing.setValue(false);
    }

    public LiveData<EarningsResponse.EarningsItem> getEarningsData() {
        return repository.getEarningsData();
    }

    public LiveData<Boolean> isLoading() {
        return repository.getIsLoading();
    }

    public LiveData<Boolean> isRefreshing() {
        return isRefreshing;
    }

    public LiveData<String> getError() {
        return repository.getError();
    }

    public boolean isLoggedIn() {
        return sessionManager.isLoggedIn();
    }

    public void fetchEarningsData() {
        if (!sessionManager.isLoggedIn()) {
            return;
        }
        
        String authorization = sessionManager.getAuthToken();
        String cookie = sessionManager.getCookie();
        
        if (authorization.isEmpty() || cookie.isEmpty()) {
            return;
        }
        
        repository.fetchEarningsData(authorization, cookie);
    }

    public void startAutoRefresh() {
        autoRefreshEnabled = true;
        scheduleNextRefresh();
    }

    public void stopAutoRefresh() {
        autoRefreshEnabled = false;
        handler.removeCallbacksAndMessages(null);
    }

    private void scheduleNextRefresh() {
        if (!autoRefreshEnabled) {
            return;
        }

        handler.postDelayed(new Runnable() {
            @Override
            public void run() {
                isRefreshing.setValue(true);
                fetchEarningsData();
                isRefreshing.setValue(false);
                scheduleNextRefresh();
            }
        }, REFRESH_INTERVAL);
    }

    @Override
    protected void onCleared() {
        super.onCleared();
        stopAutoRefresh();
    }
} 