package com.example.myapplication.data.model;

import com.google.gson.annotations.SerializedName;

import java.util.List;

public class EarningsResponse {
    @SerializedName("status")
    private int status;
    
    @SerializedName("data")
    private EarningsData data;
    
    @SerializedName("msg")
    private String message;
    
    public static class EarningsData {
        @SerializedName("list")
        private List<EarningsItem> list;
        
        @SerializedName("time_range")
        private List<String> timeRange;
        
        public List<EarningsItem> getList() {
            return list;
        }
        
        public List<String> getTimeRange() {
            return timeRange;
        }
    }
    
    public static class EarningsItem {
        @SerializedName("num")
        private String num;
        
        @SerializedName("amount")
        private String amount;
        
        public String getNum() {
            return num;
        }
        
        public String getAmount() {
            return amount;
        }
    }
    
    public int getStatus() {
        return status;
    }
    
    public EarningsData getData() {
        return data;
    }
    
    public String getMessage() {
        return message;
    }
} 