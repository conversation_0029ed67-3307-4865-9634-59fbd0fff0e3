<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_login" modulePackage="com.example.myapplication" filePath="app\src\main\res\layout\activity_login.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="android.widget.RelativeLayout"><Targets><Target tag="layout/activity_login_0" view="RelativeLayout"><Expressions/><location startLine="1" startOffset="0" endLine="63" endOffset="16"/></Target><Target id="@+id/webView" view="WebView"><Expressions/><location startLine="8" startOffset="4" endLine="12" endOffset="39"/></Target><Target id="@+id/qrCodeImageView" view="ImageView"><Expressions/><location startLine="30" startOffset="8" endLine="35" endOffset="49"/></Target><Target id="@+id/tvStatus" view="TextView"><Expressions/><location startLine="45" startOffset="8" endLine="52" endOffset="44"/></Target><Target id="@+id/progressBar" view="ProgressBar"><Expressions/><location startLine="56" startOffset="4" endLine="61" endOffset="45"/></Target></Targets></Layout>